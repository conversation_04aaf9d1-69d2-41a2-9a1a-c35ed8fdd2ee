// 全局要用的类型放到这里

declare global {
  type IResData<T> = {
    code: number
    msg: string
    data: T
  }

  // uni.uploadFile文件上传参数
  type IUniUploadFileOptions = {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  type IUserInfo = {
    nickname?: string
    avatar?: string
    /** 微信的 openid，非微信没有这个字段 */
    openid?: string
    token?: string
  }

  // 扩展微信小程序接口类型
  namespace WechatMiniprogram {
    interface Wx {
      openBusinessView: (options: {
        businessType: string
        extraData: Record<string, any>
        success?: () => void
        fail?: () => void
        complete?: () => void
      }) => void
    }
  }
}

export {} // 防止模块污染
