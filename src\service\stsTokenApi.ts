import { http } from '@/utils/http'

// 响应接口
export interface GetStsTokenRes {
  data: {
    accessKeyId: string
    accessKeySecret: string
    expiration: string
    securityToken: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 获取阿里云OSS的STS临时访问凭证
 * @returns STS临时凭证响应
 */
export const getStsTokenApi = () => http.post<GetStsTokenRes>('/sts/getStsTokenForOss')

// 评论图片上传OSS的路径
export const commentImageUploadPath = 'mall/comment'
