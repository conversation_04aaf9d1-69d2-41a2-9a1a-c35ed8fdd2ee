<script setup lang="ts">
import PriceBox from '@/components/Price/PriceBox.vue'

const props = defineProps<{
  data: any
}>()

const toContextPage = (id: number) => {
  uni.navigateTo({
    url: '/pages/tutorials/contextPage?id=' + id,
  })
}
</script>

<template>
  <view class="flex bg-white p-2 rounded mb-3 gap-1 text-sm" @click="toContextPage(data.articleId)">
    <up-image
      class="shrink-0 rounded overflow-hidden"
      :width="85"
      :height="85"
      :src="data.imageUrl"
    ></up-image>
    <view class="p-2">
      <view class="o-color-danger">{{ data.goodsName }}</view>
      <view>{{ data.goodsStatusName }}</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
