<route lang="json5">
{
  style: {
    navigationBarTitleText: '分类',
    // navigationStyle: 'custom',
  },
}
</route>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref, nextTick } from 'vue'
import { goodsClassifyListApi, mallGoodsListApi } from '@/service/shoppingMallApi'

const scrollTop = ref(0) // tab标题的滚动条位置
const current = ref(0) // 预设当前项的值
const menuHeight = ref(0) // 左边菜单的高度
const menuItemHeight = ref(0) // 左边菜单item的高度
const rightItemId = ref('')
const tabbar = ref<
  Array<{
    classifyName: string
    goodsClassifyId: number
    lists: Array<{
      goodsName: string
      goodsId: number
      imageUrl: string
    }>
  }>
>([]) // 分类数据
const arr = ref<{ top: number; bottom: number }[]>([])
const timer = ref<any>(null) // 定时器
const scrollEndTimer = ref<any>(null)
const instance = getCurrentInstance()
const isScrolling = ref(false) // 添加滚动状态标记
const rightBoxHeight = ref(0)
const refreshing = ref(false) // 下拉刷新状态

// 获取商品列表
const getMallGoodsListApi = (id: number) =>
  new Promise<
    Array<{
      goodsId: number
      goodsName: string
      imageUrl: string
    }>
  >((resolve, reject) => {
    mallGoodsListApi({ goodsClassifyId: id })
      .then((res) => {
        resolve(res.data)
      })
      .catch((err) => {
        reject(err)
      })
  })

// 初始化分类数据
const initTabbar = async () => {
  try {
    const res = await goodsClassifyListApi({})
    if (res.success && res.data) {
      // 转换数据结构
      tabbar.value = res.data.map((item) => ({
        classifyName: item.classifyName,
        goodsClassifyId: item.goodsClassifyId,
        lists: [], // 初始化为空数组，后续加载商品数据
      }))

      // 一次性加载所有分类的商品数据
      const loadAllGoods = async () => {
        const promises = tabbar.value.map(async (category) => {
          try {
            const goodsRes = await getMallGoodsListApi(category.goodsClassifyId)
            if (goodsRes) {
              category.lists = goodsRes.map((goods) => ({
                goodsName: goods.goodsName,
                goodsId: goods.goodsId,
                imageUrl: goods.imageUrl,
              }))
            }
          } catch (error) {
            console.error(`加载分类 ${category.classifyName} 的商品数据失败:`, error)
          }
        })

        await Promise.all(promises)
      }

      await loadAllGoods()
    }
  } catch (error) {
    console.error('初始化分类数据失败:', error)
  }
}

// 处理下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    await initTabbar()
    nextTick(() => {
      getMenuItemTop()
    })
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1000,
    })
  } catch (error) {
    console.error('刷新数据失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error',
      duration: 1000,
    })
  } finally {
    refreshing.value = false
  }
}

const getElRect = (selector: string): Promise<UniApp.NodeInfo | null> => {
  return new Promise((resolve) => {
    const query = uni.createSelectorQuery().in(instance)
    query
      .select(selector)
      .fields({ size: true }, (res) => {
        if (!res) {
          setTimeout(() => {
            resolve(getElRect(selector))
          }, 100)
        } else {
          resolve(res as UniApp.NodeInfo)
        }
      })
      .exec()
  })
}

const getMenuItemTop = () => {
  return new Promise((resolve) => {
    const selectorQuery = uni.createSelectorQuery().in(instance)
    selectorQuery
      .selectAll('.class-item')
      .boundingClientRect((rects: UniApp.NodeInfo[]) => {
        if (!rects || rects.length === 0) {
          setTimeout(() => {
            getMenuItemTop()
          }, 100)
          return
        }
        const initialTop = rects[0]?.top || 0
        arr.value = rects.map((rect) => ({
          top: rect.top - initialTop,
          bottom: rect.top - initialTop + rect.height,
        }))
        resolve(true)
      })
      .exec()
  })
}

const leftMenuStatus = async (index: number) => {
  current.value = index
  if (menuHeight.value === 0 || menuItemHeight.value === 0) {
    const menuRect = await getElRect('.menu-scroll-view')
    if (menuRect) {
      menuHeight.value = menuRect.height
    }
    const itemRect = await getElRect('.u-tab-item')
    if (itemRect) {
      menuItemHeight.value = itemRect.height
    }
  }
  if (menuItemHeight.value > 0) {
    scrollTop.value = index * menuItemHeight.value + menuItemHeight.value / 2 - menuHeight.value / 2
  }
}

const swichMenu = async (index: number) => {
  if (current.value === index) {
    return
  }
  current.value = index
  leftMenuStatus(index)

  isScrolling.value = true
  rightItemId.value = ''
  nextTick(() => {
    rightItemId.value = `tabItem${index}`
  })
}

const rightScroll = async (e: any) => {
  if (isScrolling.value) {
    clearTimeout(scrollEndTimer.value)
    scrollEndTimer.value = setTimeout(() => {
      isScrolling.value = false
    }, 150)
    return
  }

  if (arr.value.length === 0) {
    await getMenuItemTop()
  }

  if (rightBoxHeight.value === 0) {
    const rightBoxRect = await getElRect('#right-box')
    if (rightBoxRect) {
      rightBoxHeight.value = rightBoxRect.height
    }
  }

  clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    const scrollTop = e.detail.scrollTop
    const viewportBottom = scrollTop + rightBoxHeight.value

    let maxIntersection = -1
    let activeIndex = -1

    for (let i = 0; i < arr.value.length; i++) {
      const item = arr.value[i]
      const intersectionTop = Math.max(item.top, scrollTop)
      const intersectionBottom = Math.min(item.bottom, viewportBottom)
      const intersection = Math.max(0, intersectionBottom - intersectionTop)

      if (intersection > maxIntersection) {
        maxIntersection = intersection
        activeIndex = i
      }
    }

    if (activeIndex !== -1 && current.value !== activeIndex) {
      leftMenuStatus(activeIndex)
    }
  }, 100)
}

const handleSearch = () => {
  uni.navigateTo({
    url: '/pages/shoppingMall/searchPage',
  })
}

const handleGoodClick = (goodsId: number) => {
  if (goodsId !== 0) {
    uni.navigateTo({
      url: `/pages/goodsDetail/index?goodsId=${goodsId}`,
    })
  }
}
onMounted(async () => {
  await initTabbar()
  nextTick(() => {
    getMenuItemTop()
  })
})

// TODO 本页切换回首页后，等一段时间后，不断警告：
// 数据传输长度过长 setData 数据传输长度为 1405 KB，存在有性能问题！
</script>

<template>
  <view class="flex flex-col h-100vh">
    <!-- <view class="bg-white px-4 py-2">
      <view class="flex items-center o-color-aid o-bg-no px-4 py-1 rd-full" @click="handleSearch">
        <up-icon name="search" :size="20"></up-icon>
        <text class="ml-2 text-sm">搜索</text>
      </view>
    </view> -->
    <view class="flex flex-1 overflow-hidden">
      <scroll-view
        scroll-y
        scroll-with-animation
        class="u-tab-view menu-scroll-view"
        :scroll-top="scrollTop"
      >
        <view
          v-for="(tabItem, tabIndex) in tabbar"
          :key="tabIndex"
          class="u-tab-item flex items-center justify-center transition"
          :class="[current == tabIndex ? 'u-tab-item-active' : '']"
          @tap.stop="swichMenu(tabIndex)"
        >
          {{ tabItem.classifyName }}
        </view>
      </scroll-view>
      <scroll-view
        scroll-y
        scroll-with-animation
        id="right-box"
        @scroll="rightScroll"
        :scroll-into-view="rightItemId"
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
      >
        <view class="space-y-3">
          <view
            class="class-item bg-white"
            v-for="(tabItem, tabIndex) in tabbar"
            :id="'tabItem' + tabIndex"
            :key="tabIndex"
          >
            <view class="text-lg px-4 pt-4 pb-2 font-bold text-primary">
              {{ tabItem.classifyName }}
            </view>
            <view class="flex flex-col space-y-2">
              <view
                class="f-card p-4"
                v-for="goodItem in tabItem.lists"
                :key="'goodItem' + goodItem.goodsId"
                @click="handleGoodClick(goodItem.goodsId)"
              >
                <image class="w-full" :src="goodItem.imageUrl" mode="widthFix"></image>
                <view class="pt-2">{{ goodItem.goodsName }}</view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.u-tab-view {
  max-width: 200rpx;
  height: 100%;
}

.u-tab-item {
  box-sizing: border-box;
  height: 110rpx;
  line-height: 1;
  color: #444;
}

.u-tab-item-active {
  @apply font-bold relative text-primary bg-white;
}

.u-tab-item-active::before {
  position: absolute;
  top: 39rpx;
  left: 0;
  height: 32rpx;
  content: '';
  border-left: 4px solid var(--wot-color-theme);
}

.f-card {
  border-bottom: 2px solid var(--o-body-bg-color);
}

.class-item:last-child {
  min-height: 100vh;
}
</style>
