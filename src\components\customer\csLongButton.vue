<script setup lang="ts">
import { jumpToWeChatCustomerService } from '@/hooks/useCustomerService'

withDefaults(
  defineProps<{
    content?: string
  }>(),
  {
    content: '咨询客服',
  },
)
</script>

<template>
  <button
    class="o-customer-service-long flex justify-center items-center text-sm p-3 mt-3 space-x-1"
    @click="jumpToWeChatCustomerService"
  >
    <up-icon :size="30" name="chat"></up-icon>
    <text>{{ content }}</text>
  </button>
</template>

<style scoped lang="scss"></style>
