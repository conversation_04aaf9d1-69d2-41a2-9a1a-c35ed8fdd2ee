import { http } from '@/utils/http'

// 参数接口
export interface AddressCreateParams {
  addressDetail: string
  district?: string
  isDefault: boolean
  phone: string
  realName: string
  userId: number
}

// 响应接口
export interface AddressCreateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 新增收货地址
 * @param {object} params cmd
 * @param {string} params.addressDetail 收货人详细地址
 * @param {string} params.district 收货人所在区
 * @param {boolean} params.isDefault 是否默认地址
 * @param {string} params.phone 收货人电话
 * @param {string} params.realName 收货人姓名
 * @param {number} params.userId 用户id
 * @returns
 */
export const addressCreateApi = (params: AddressCreateParams) =>
  http.post<AddressCreateRes>('/api/addressCreate', params)

// 参数接口
export interface AddressDelParams {
  addressId: number
}

// 响应接口
export interface AddressDelRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 删除收货地址
 * @param {object} params cmd
 * @param {number} params.addressId 地址id
 * @returns
 */
export const addressDelApi = (params: AddressDelParams) =>
  http.post<AddressDelRes>('/api/addressDel', params)

// 参数接口
export interface AddressLoadParams {
  addressId: number
}

// 响应接口
export interface AddressLoadRes {
  data: {
    addressDetail: string
    addressId: number
    createdBy: string
    createdDate: Record<string, unknown>
    district: string
    isDefault: boolean
    phone: string
    realName: string
    userId: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 加载收货地址
 * @returns
 * @param params
 */
export const addressLoadApi = (params: AddressLoadParams) =>
  http.post<AddressLoadRes>('/api/addressLoad', params)

// 参数接口
export interface AddressPageParams {
  groupBy?: string
  isDefault?: boolean
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirectionType
  pageIndex?: number
  pageSize?: number
  userId: number
}

// 响应接口
export interface AddressPageRes {
  data: {
    addressDetail: string
    addressId: number
    createdBy: string
    createdDate: Record<string, unknown>
    district: string
    isDefault: boolean
    phone: string
    realName: string
    userId: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 收货地址分页
 * @param {object} params qry
 * @param {string} params.groupBy
 * @param {boolean} params.isDefault 是否默认地址
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.userId 用户id
 * @returns
 */
export const addressPageApi = (params: AddressPageParams) =>
  http.post<AddressPageRes>('/api/addressPage', params)

// 参数接口
export interface AddressUpdateParams {
  addressDetail: string
  addressId: number
  district?: string
  isDefault: boolean
  phone: string
  realName: string
  userId: number
}

// 响应接口
export interface AddressUpdateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 编辑收货地址
 * @param {object} params cmd
 * @param {string} params.addressDetail 收货人详细地址
 * @param {number} params.addressId 地址id
 * @param {string} params.district 收货人所在区
 * @param {boolean} params.isDefault 是否默认地址
 * @param {string} params.phone 收货人电话
 * @param {string} params.realName 收货人姓名
 * @param {number} params.userId 用户id
 * @returns
 */
export const addressUpdateApi = (params: AddressUpdateParams) =>
  http.post<AddressUpdateRes>('/api/addressUpdate', params)
