// TODO 改路径
export const PublicImgPath = 'https://app.xunma.store/'
// export const PublicImgPath = '/static/images/'

export const DesignImage = {
  // 首页
  indexPage: {
    // 轮播图
    carouses: [
      {
        url: '/pages/makeFilm/index',
        img: PublicImgPath + 'p_index_carouse_1X.jpg',
      },
      {
        url: '/pages/tutorials/violation',
        img: PublicImgPath + 'p_index_carouse_2X.jpg',
      },
    ],
  },
  tutorials: {
    defaultMainImg: PublicImgPath + 'p_tutorials_defaultMainImg.png',
  },
  designServer: {
    carouses: [
      PublicImgPath + 'designServer_01.jpg',
      PublicImgPath + 'designServer_02.jpg',
      PublicImgPath + 'designServer_03.jpg',
      PublicImgPath + 'designServer_04.jpg',
      PublicImgPath + 'designServer_05.jpg',
    ],
  },
  labelPrint: {
    carouses: [
      PublicImgPath + 'labelPrint_01.jpg',
      PublicImgPath + 'labelPrint_02.jpg',
      PublicImgPath + 'labelPrint_03.jpg',
    ],
  },
  miniShop: {
    carouses: [
      PublicImgPath + 'miniShop_01.jpg',
      PublicImgPath + 'miniShop_02.jpg',
      PublicImgPath + 'miniShop_03.jpg',
      PublicImgPath + 'miniShop_04.jpg',
    ],
  },
}
