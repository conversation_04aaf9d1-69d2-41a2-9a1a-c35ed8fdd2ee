<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{ style: { navigationBarTitleText: '规范学习' } }
</route>
<script lang="ts" setup>
import { articleListApi, ArticleListData, ArticleListRes } from '@/service/tutorialsApi'
import ArticleCard from '@/components/tutorials/articleCard.vue'
import { Color } from '@/enums/colorEnum'

const tab = ref<number>(0)
const list = ref<ArticleListData[]>()

const scrollTop = ref<number>(0)
onPageScroll((e) => {
  scrollTop.value = e.scrollTop
})

const {
  loading: tabLoading,
  error: tabError,
  data: tabList,
  run: tabRun,
} = useRequest<ArticleListRes>(() => articleListApi({ articleTypeId: 1 }), {
  immediate: false,
  transform: (res) =>
    res[0].itemList.map((item) => {
      return { ...item, name: item.typeName }
    }),
})

onMounted(() => {
  tabRun().then(() => {
    getListData()
  })
})

const getListData = () => {
  list.value = tabList.value[tab.value].articleList
}

const handleSearch = () => {
  uni.navigateTo({
    url: '/pages/tutorials/searchPage',
  })
}
</script>
<template>
  <view class="py-2 px-4 bg-white" @click="handleSearch">
    <view class="o-bg-no flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
      <up-input
        :maxlength="50"
        border="none"
        class="grow o-bg-transparent"
        clearable
        placeholder="搜索标题"
      ></up-input>
      <up-icon name="search" :size="20"></up-icon>
    </view>
  </view>
  <up-sticky bgColor="#fff">
    <up-tabs
      v-model:current="tab"
      :lineWidth="50"
      :list="tabList"
      @change="getListData"
      :lineColor="Color.primary"
    ></up-tabs>
  </up-sticky>
  <view class="o-bg-no">
    <view class="px-3 pt-3 pb-6">
      <article-card v-for="subItem in list" :key="subItem.articleId" :data="subItem" />
    </view>
  </view>
  <up-back-top :scroll-top="scrollTop"></up-back-top>
</template>

<style lang="scss" scoped></style>
