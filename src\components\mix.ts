import { getDouYinOrderPayStateApi } from '@/service/orderApi'
import { PayState } from '@/enums'

// 微信支付参数类型
interface WxPaymentParams {
  timeStamp: string
  nonceStr: string
  packageValue: string
  signType: 'MD5' | 'HMAC-SHA256' | 'RSA'
  paySign: string
}
// 微信支付方法
export const wxRequestPayment = (params: WxPaymentParams) =>
  new Promise((resolve, reject) => {
    wx.requestPayment({
      ...params,
      package: params.packageValue,
      success: resolve,
      fail: (err) => {
        showPayFailed('001')
        reject(err)
      },
    })
  })

// 抖音支付参数类型
interface DouYinPaymentParams {
  byteAuthorization: string
  data: string
  isZeroOrder: boolean
  orderCode: string
}

// 支付状态轮询结果类型
type PollResult = {
  success: boolean
  errorType?: 'payment' | 'network'
}

// 通用延迟方法
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// 支付状态轮询方法
const pollPaymentStatus = async (
  orderCode: string,
  retries: number,
  delays: number[],
): Promise<PollResult> => {
  let lastErrorType: 'payment' | 'network' | undefined

  for (let attempt = 0; attempt < retries; attempt++) {
    await delay(delays[attempt])

    try {
      const res = await getDouYinOrderPayStateApi({ orderCode })
      if (res.data.payState === PayState.paid) {
        return { success: true }
      }
      lastErrorType = 'payment'
    } catch (error) {
      lastErrorType = 'network'
    }
  }

  return { success: false, errorType: lastErrorType }
}

// 显示支付失败弹窗
const showPaymentResultModal = (errorType?: 'payment' | 'network') => {
  const isNetworkError = errorType === 'network'
  uni.showModal({
    title: isNetworkError ? '网络错误' : '支付失败',
    content: isNetworkError ? '未知订单支付状态，请稍后查看' : '支付未完成，请检查订单状态',
    showCancel: false,
    confirmColor: '#000000',
  })
}
// 抖音支付方法
export const douYinRequestPayment = (params: DouYinPaymentParams) =>
  new Promise((resolve, reject) => {
    // 第一步：请求订单
    new Promise<any>((res, rej) => {
      tt.requestOrder({
        data: params.data,
        byteAuthorization: params.byteAuthorization,
        success: res,
        fail: rej,
      })
    })
      .then((orderRes) => {
        // 第二步：处理支付结果
        return new Promise<void>((paymentResolve, paymentReject) => {
          tt.getOrderPayment({
            orderId: orderRes.orderId,
            success: async () => {
              // 轮询支付状态（1s -> 1.5s -> 4s）
              const result = await pollPaymentStatus(params.orderCode, 3, [1000, 1500, 4000])
              if (result.success) {
                resolve(true)
              } else {
                showPaymentResultModal(result.errorType)
                paymentReject(new Error('Payment timeout'))
              }
            },
            fail: async (err) => {
              if (err.errNo === 9) {
                // 特定错误码处理
                const result = await pollPaymentStatus(params.orderCode, 3, [1000, 1500, 4000])
                if (result.success) {
                  resolve(true)
                } else {
                  showPaymentResultModal(result.errorType)
                  paymentReject(new Error('Payment timeout'))
                }
              } else {
                showPayFailed('002')
                paymentReject(err)
              }
            },
          })
        })
      })
      .catch((error) => {
        console.error('支付流程异常:', error)
        showPayFailed('003')
        reject(error)
      })
  })

// 通用支付失败提示
export const showPayFailed = (code: string) => {
  uni.showModal({
    title: `支付失败 #${code}`,
    content: '请稍后再试',
    showCancel: false,
    confirmColor: '#000000',
  })
}
