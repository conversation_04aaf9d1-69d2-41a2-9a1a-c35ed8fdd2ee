<script setup lang="ts">
import { Color } from '@/enums/colorEnum'
</script>

<template>
  <view class="back-to-top">
    <up-icon :color="Color.gray" :size="18" name="arrow-upward" />
  </view>
</template>

<style scoped lang="scss">
.back-to-top {
  border-radius: 50%;
  z-index: 10;
  bottom: 100px;
  right: 20px;
  position: fixed;
  background-color: #e1e1e1;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}
</style>
