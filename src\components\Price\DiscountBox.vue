<script lang="ts" setup>
const props = defineProps<{
  discount: number
  size: number
}>()

const price1 = computed(() => {
  const d = props.discount * 10
  return d?.toFixed(1).split('.')[0]
})
const price2 = computed(() => {
  const d = props.discount * 10
  return d?.toFixed(1).split('.')[1]
})
</script>
<template>
  <view class="flex-shrink-0 flex items-baseline font-bold">
    <text :style="{ fontSize: size < 24 ? 24 : size + 'rpx' }">
      {{ price1 }}
    </text>
    <text v-if="price2 !== '0'" :style="{ fontSize: size - 18 < 24 ? 24 : size - 18 + 'rpx' }">
      .{{ price2 }}
    </text>
    <text class="pl-1" :style="{ fontSize: size - 18 < 24 ? 24 : size - 18 + 'rpx' }">折</text>
  </view>
</template>
<style lang="scss" scoped></style>
