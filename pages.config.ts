import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'barcode_center_wx',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
      '^u--(.*)': 'uview-plus/components/u-$1/u-$1.vue',
      '^up-(.*)': 'uview-plus/components/u-$1/u-$1.vue',
      '^u-([^-].*)': 'uview-plus/components/u-$1/u-$1.vue',
    },
  },
  /* // 这个是微信的地址输入插件，不用
    plugins: {
    'address-form': {
      version: '1.0.2', // 插件版本
      provider: 'wx57d7ae552cbb3084',
      export: 'config.js', // 步骤3.2的配置文件
    },
  }, */
  tabBar: {
    color: '#999999',
    selectedColor: '#FF5B00',
    backgroundColor: '#F8F8F8',
    borderStyle: 'black',
    height: '50px',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    list: [
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/homeH.png',
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/list.png',
        selectedIconPath: 'static/tabbar/listH.png',
        pagePath: 'pages/shoppingMall/index',
        text: '分类',
      },
      {
        iconPath: 'static/tabbar/user.png',
        selectedIconPath: 'static/tabbar/userH.png',
        pagePath: 'pages/userPage/index',
        text: '我的',
      },
    ],
  },
  condition: {
    // 模式配置，仅开发期间生效
    current: 2, // 当前激活的模式(list 的索引项)
    list: [
      {
        name: 'indexWithQuery',
        path: 'pages/index/index',
        query: 'scene=emailop%3DXGxvB',
      },
      {
        name: 'shoppingMall',
        path: 'pages/shoppingMall/index',
        query: '',
      },
      {
        name: 'userPage',
        path: 'pages/userPage/index',
        query: '',
      },
      {
        name: 'myOrder',
        path: 'pages/myOrder/index',
        query: '',
      },
    ],
  },
})
