<script setup lang="ts">
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { useServiceStore } from '@/store/serviceStore'
import { storeToRefs } from 'pinia'

const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
</script>

<template>
  <view :class="DESCRIPTION_STR[descriptionServiceType].colorClass" class="pl-2">
    <view class="sf-title-round pt-4 pl-3 pb-2">
      <view class="sf-title-text text-2xl font-bold">
        {{ DESCRIPTION_STR[descriptionServiceType].title }}
      </view>
      <view class="text-sm o-color-aid">
        {{ DESCRIPTION_STR[descriptionServiceType].typeStr }}
      </view>
      <view class="text-xs">{{ DESCRIPTION_STR[descriptionServiceType].description }}</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
