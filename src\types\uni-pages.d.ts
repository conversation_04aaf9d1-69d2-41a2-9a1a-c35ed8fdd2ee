/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/about/filmSpecification" |
       "/pages/about/index" |
       "/pages/about/statement" |
       "/pages/addressPage/editor" |
       "/pages/addressPage/index" |
       "/pages/agencyService/index" |
       "/pages/agencyService/modifyService" |
       "/pages/agencyService/registerService" |
       "/pages/agencyService/renewalService" |
       "/pages/designServer/index" |
       "/pages/discountCoupon/index" |
       "/pages/goodsDetail/index" |
       "/pages/importedGoods/index" |
       "/pages/infoReport/index" |
       "/pages/infoReport/myInfoReport" |
       "/pages/invoiceTemple/editor" |
       "/pages/invoiceTemple/index" |
       "/pages/labelPrint/index" |
       "/pages/makeFilm/index" |
       "/pages/makeFilm/vendorCodePage" |
       "/pages/miniShop/index" |
       "/pages/miniShop/viewWebPage" |
       "/pages/myAgency/index" |
       "/pages/myAgency/submitModify" |
       "/pages/myAgency/submitRegister" |
       "/pages/myAgency/submitRenewal" |
       "/pages/myOrder/commentOrder" |
       "/pages/myOrder/index" |
       "/pages/myOrder/invoiceInfo" |
       "/pages/myOrder/logisticsDetail" |
       "/pages/myOrder/logisticsOrder" |
       "/pages/myOrder/orderDetail" |
       "/pages/myOrder/orderInvoicing" |
       "/pages/myOrder/refundApply" |
       "/pages/myOrder/refundDetail" |
       "/pages/orderConfirm/default" |
       "/pages/orderConfirm/infoReport" |
       "/pages/orderConfirm/labelPrint" |
       "/pages/orderConfirm/makeFilm" |
       "/pages/orderConfirm/miniShop" |
       "/pages/orderConfirm/shoppingMall" |
       "/pages/orderConfirm/storeCode" |
       "/pages/paymentSuccess/index" |
       "/pages/shoppingMall/index" |
       "/pages/shoppingMall/searchPage" |
       "/pages/tutorials/contextPage" |
       "/pages/tutorials/index" |
       "/pages/tutorials/violation" |
       "/pages/userPage/index" |
       "/pages/userPage/myInformation";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/shoppingMall/index" | "/pages/userPage/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
