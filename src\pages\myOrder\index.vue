<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的订单',
    backgroundColor: '#f0f3f8',
  },
}
</route>
<script lang="ts" setup>
import PriceBox from '@/components/Price/PriceBox.vue'
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { storeToRefs } from 'pinia'
import {
  allOrderPageApi,
  AllOrderPageRes,
  AllOrderPageResData,
  confirmReceiptApi,
} from '@/service/orderApi'
import { OrderDirection } from '@/enums/httpEnum'
import { useUserStore } from '@/store/user'
import { OrderStatus, PayState } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { useRefundStore } from '@/store/refundStore'
import { useCommentStore } from '@/store/commentStore'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList, orderSuccess } = storeToRefs(useInvoiceOrderStore)
const refundStore = useRefundStore()
const showEmptyIcon = ref(false)
const orderCode = ref([])
const page = ref(1)
const list = ref<AllOrderPageResData[]>([])
// const selectValue = ref<AllOrderPageResData[]>([])
const tabsList = [{ name: '全部' }, { name: '待发货' }, { name: '待收货' }, { name: '退款售后' }]
const currentTab = ref(0)
const refresherTriggered = ref(false)
const loading = ref(false)

const getOrderList = () =>
  new Promise((resolve, reject) => {
    loading.value = true // 请求前设置loading状态
    allOrderPageApi({
      // serverType:
      // isInvoiced: getIsInvoiced(),
      orderStutas: getOrderStatus(),
      userId: userId.value,
      payState: PayState.paid,
      groupBy: '',
      needTotalCount: true,
      // orderBy: 'payDate',
      orderDirection: OrderDirection.desc,
      pageIndex: page.value,
      pageSize: 50,
    })
      .then((res: AllOrderPageRes) => {
        if (res.data.length === 0) {
          showEmptyIcon.value = true
        } else {
          showEmptyIcon.value = false
        }
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
      .finally(() => {
        loading.value = false // 请求完成后重置loading状态
      })
  })

onLoad((options) => {
  // 页面加载,清空orderCode，后退到此页面时，不触发onLoad
  if (options.tab) {
    switch (options.tab) {
      case 'all':
        currentTab.value = 0
        break
      case 'waitSend':
        currentTab.value = 1
        break
      case 'waitReceive':
        currentTab.value = 2
        break
      case 'refund':
        currentTab.value = 3
        break
      default:
        currentTab.value = 0
        break
    }
  }
})

onShow(() => {
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList().then((res: AllOrderPageRes) => {
    list.value = res.data
  })
})

// scroll-view 下拉刷新
const onRefresh = () => {
  refresherTriggered.value = true
  showEmptyIcon.value = false
  page.value = 1
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList()
    .then((res: AllOrderPageRes) => {
      list.value = res.data
    })
    .catch(() => {
      uni.showToast({
        title: '刷新失败',
        icon: 'none',
      })
    })
    .finally(() => {
      refresherTriggered.value = false
    })
}

// scroll-view 上拉加载更多
const onLoadMore = () => {
  if (!showEmptyIcon.value && !loading.value) {
    page.value = page.value + 1
    getOrderList()
      .then((res: AllOrderPageRes) => {
        if (res && res.data.length > 0) {
          list.value = [...list.value, ...res.data]
        } else {
          // 没有更多数据
          showEmptyIcon.value = true
        }
      })
      .catch(() => {
        // 加载失败时回退页码
        page.value--
        uni.showToast({
          title: '加载更多失败',
          icon: 'none',
        })
      })
  }
}

const getOrderStatus = () => {
  switch (currentTab.value) {
    case 0:
      // all
      return []
    case 1:
      // waitSend
      return [OrderStatus.waitingSend]
    case 2:
      // waitReceive
      return [OrderStatus.waitingReceive]
    case 3:
      // refund
      return [OrderStatus.afterSale, OrderStatus.afterSaleEnd]
    default:
      return []
  }
}

const getOrderStatusColor = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.waitingSend:
      return 'text-orange'
    case OrderStatus.waitingReceive:
      return 'text-primary'
    case OrderStatus.waitingEvaluate:
      return 'text-primary'
    case OrderStatus.done:
      return 'text-gray'
    case OrderStatus.afterSale:
      return 'text-red'
    case OrderStatus.afterSaleEnd:
      return 'text-gray'
    default:
      return 'text-gray'
  }
}

const handleFilter = () => {
  page.value = 1
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList()
    .then((res: AllOrderPageRes) => {
      list.value = res.data
    })
    .catch(() => {
      uni.showToast({
        title: '获取数据失败',
        icon: 'none',
      })
    })
}

const handleViewLogistics = (orderCode: string) => {
  uni.navigateTo({
    url: '/pages/myOrder/logisticsDetail?orderCode=' + orderCode,
  })
}

const viewOrderDetail = (orderPayCode: string) => {
  uni.navigateTo({
    url: '/pages/myOrder/orderDetail?orderPayCode=' + orderPayCode,
  })
}

const handleConfirmReceipt = (data: AllOrderPageResData) => {
  const { transactionNo, orderCode } = data
  wx?.openBusinessView({
    businessType: 'weappOrderConfirm',
    extraData: {
      transaction_id: transactionNo,
    },
    success() {
      confirmReceiptApi({
        orderCode,
      })
        .then((res) => {
          if (res.success) {
            getOrderList()
          }
        })
        .catch((err) => {
          uni.showToast({
            icon: 'error',
            title: err,
            duration: 3000,
          })
        })
    },
    fail() {
      uni.showToast({
        icon: 'error',
        title: '确认收货失败',
        duration: 3000,
      })
    },
  })
}

const handleApplyAfterSale = (orderCode: string, item: AllOrderPageResData) => {
  // 设置售后申请商品信息
  refundStore.setRefundGoodsInfo({
    orderCode,
    goodsName: item.otherOrderParamDTO?.goodsName,
    orderContent: item.otherOrderParamDTO?.orderContent,
    goodsImage: item.otherOrderParamDTO?.goodsImage,
    payDate: item.payDate,
  })
  uni.navigateTo({
    url: '/pages/myOrder/refundApply?orderCode=' + orderCode,
  })
}

// 查看售后详情
const viewRefundDetail = (refundOrderId: number) => {
  uni.navigateTo({
    url: `/pages/myOrder/refundDetail?refundOrderId=${refundOrderId}`,
  })
}

// 打开评价页面
const openCommentModal = (item: AllOrderPageResData) => {
  const commentStore = useCommentStore()
  commentStore.setCommentInfo({
    orderId: item.orderId,
    orderCode: item.orderCode,
    goodsName: item.otherOrderParamDTO?.goodsName || '',
    goodsImage: item.otherOrderParamDTO?.goodsImage || '',
    orderContent: item.otherOrderParamDTO?.orderContent || '',
  })
  uni.navigateTo({
    url: '/pages/myOrder/commentOrder',
  })
}
</script>
<template>
  <up-sticky bgColor="#fff">
    <up-tabs
      :lineColor="Color.primary"
      :lineWidth="90"
      :scrollable="false"
      :list="tabsList"
      v-model:current="currentTab"
      @change="handleFilter"
    ></up-tabs>
  </up-sticky>
  <scroll-view
    :scroll-y="true"
    :refresher-enabled="true"
    :refresher-triggered="refresherTriggered"
    @refresherrefresh="onRefresh"
    @scrolltolower="onLoadMore"
    :style="{ height: 'calc(100vh - 44px)' }"
  >
    <div class="px-3">
      <view
        class="bg-white p-3 rounded-sm mt-2"
        v-for="item in list"
        :key="`${item.serverType}${item.orderId}`"
      >
        <view class="flex items-center justify-between" @click="viewOrderDetail(item.orderPayCode)">
          <view>
            <view class="o-color-aid text-xs">成交时间：{{ item.payDate }}</view>
            <view class="o-color-aid text-xs">订单编号：{{ item.orderCode }}</view>
          </view>
          <view class="font-bold" :class="getOrderStatusColor(item.orderStutas)">
            {{ item.orderStutasStr }}
          </view>
        </view>
        <view class="mt-2 flex space-x-2" @click="viewOrderDetail(item.orderPayCode)">
          <image
            v-if="item.otherOrderParamDTO?.goodsImage"
            class="f-img"
            mode="aspectFill"
            :src="item.otherOrderParamDTO?.goodsImage"
          />
          <view v-else class="f-img bg-gray-200"></view>
          <view>
            <view class="truncate">{{ item.otherOrderParamDTO?.goodsName }}</view>
            <view class="text-gray text-sm">{{ item.otherOrderParamDTO?.orderContent }}</view>
          </view>
        </view>
        <view class="flex justify-between items-end gap-3">
          <view></view>
          <view class="flex shrink-0 justify-end">
            <view class="flex items-baseline">
              <view class="text-sm">实付：</view>
              <price-box :price="item.actuallyPrice" :size="36" />
            </view>
          </view>
        </view>
        <view v-if="item.orderStutas !== OrderStatus.waitingSend" class="o-line mt-2 mb-2"></view>
        <view class="flex justify-end space-x-2">
          <view
            v-if="[OrderStatus.waitingReceive, OrderStatus.done].includes(item.orderStutas)"
            class="px-5 py-2 text-sm center bg-gray-100 rounded-sm"
            @click="handleViewLogistics(item.orderCode)"
          >
            查看物流
          </view>
          <view
            v-if="item.orderStutas === OrderStatus.waitingReceive"
            class="px-5 py-2 text-sm center rounded-sm text-white bg-primary"
            @click="handleConfirmReceipt(item)"
          >
            确认收货
          </view>
          <view
            v-if="[OrderStatus.waitingEvaluate, OrderStatus.done].includes(item.orderStutas)"
            class="px-5 py-2 text-sm center bg-gray-100 rounded-sm"
            @click="handleApplyAfterSale(item.orderCode, item)"
          >
            申请售后
          </view>
          <view
            v-if="[OrderStatus.afterSale, OrderStatus.afterSaleEnd].includes(item.orderStutas)"
            class="px-5 py-2 text-sm center bg-gray-100 rounded-sm"
            @click="viewRefundDetail(item.refundOrderId)"
          >
            售后详情
          </view>
          <view
            v-if="item.orderStutas === OrderStatus.waitingEvaluate"
            class="px-5 py-2 text-sm center rounded-sm text-white bg-primary"
            @click="openCommentModal(item)"
          >
            评价
          </view>
        </view>
      </view>
      <up-empty
        v-if="list?.length === 0"
        icon="https://wx.gs1helper.com/images/common/search.png"
        text="暂无订单"
      ></up-empty>
    </div>
    <view class="w-full o-color-aid text-center pt-4 pb-10" v-if="showEmptyIcon">
      - 已经到底了 -
    </view>
  </scroll-view>
</template>

<style lang="scss" scoped>
.f-img {
  $w: 100rpx;
  width: $w;
  height: $w;
}
</style>
