<script setup lang="ts">
const props = defineProps<{
  carousesList: string[]
  windowWidth: number
  safeAreaInsets: {
    top: number
    bottom: number
  }
  statusBarHeight: number
}>()

const current = ref(0)

const handleBack = () => {
  uni.navigateBack()
}

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    urls: props.carousesList,
    current: index,
  })
}
</script>

<template>
  <view class="bg-white">
    <view class="bg-primary" :style="{ paddingTop: statusBarHeight + 'px' }">
      <up-swiper
        v-model:current="current"
        :height="windowWidth"
        :list="carousesList"
        indicator
        :autoplay="false"
        indicatorStyle="right: 20px"
        @click="previewImage(current)"
      >
        <template #indicator>
          <view
            class="px-3 py-1 rd-6 flex justify-center"
            style="background-color: rgba(0, 0, 0, 0.35)"
          >
            <text class="text-xs color-white">{{ current + 1 }}/{{ carousesList.length }}</text>
          </view>
        </template>
      </up-swiper>
    </view>

    <view
      class="f-back center rounded fixed left-4 z-1"
      :style="{ top: safeAreaInsets?.top + 6 + 'px' }"
      @click="handleBack"
    >
      <up-icon class="ml--0.5" name="arrow-left" :size="14"></up-icon>
    </view>
  </view>
</template>

<style scoped lang="scss">
.f-back {
  $w: 7vw;
  width: $w;
  height: $w;
  background-color: rgba(255, 255, 255, 0.7);
}
</style>
