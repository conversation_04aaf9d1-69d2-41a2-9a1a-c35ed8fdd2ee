<script lang="ts" setup>
import { downloadByOrderApi } from '@/service/barcodePageApi'
import { storeToRefs } from 'pinia'
import { orderPageApi, OrderPageRes } from '@/service/orderApi'
import { PayState, ServerType } from '@/enums'
import { OrderDirection } from '@/enums/httpEnum'
import { useUserStore } from '@/store/user'
import BackTop from '@/pages/about/components/backTop/backTop.vue'
import { useScrollViewBackTop } from '@/pages/about/components/backTop/useScrollViewBackTop'
import { msgModalStore } from '@/store/msgModalStore'
import { handleCopy } from '@/utils'
import { validateMail } from '@/utils/tool'

const useMsgModalStore = msgModalStore()

const userStore = useUserStore()
const { userId, downloadEmail } = storeToRefs(userStore)

const showEmptyIcon = ref(false)
const page = ref(1)
const refreshTriggered = ref(false)
const isModalShow = ref(false)
const isLoading = ref(false)
const model = reactive({
  email: downloadEmail.value,
})
const data = ref<OrderPageRes['data']>()
let orderId

const getOrderData = () =>
  new Promise((resolve, reject) => {
    orderPageApi({
      // serverType: ServerType.makeFilm, // 店内码也要囊括在内
      userId: userId.value,
      payState: PayState.paid,
      groupBy: '',
      needTotalCount: true,
      orderBy: 'payDate',
      orderDirection: OrderDirection.desc,
      pageIndex: page.value,
      pageSize: 50,
    })
      .then((res) => {
        data.value = res.data.filter((item) =>
          [ServerType.makeFilm, ServerType.storeCode].includes(item.serverType),
        )
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })

onMounted(() => {
  getOrderData()
})

const onRefresh = () => {
  if (refreshTriggered.value) return
  refreshTriggered.value = true
  getOrderData().finally(() => {
    refreshTriggered.value = false
  })
}

const handleDown = (id: number) => {
  isModalShow.value = true
  orderId = id
}

const getQuantity = (startBarCode: string, endBarCode?: string) => {
  // 去掉startBarCode最后一位
  const startNum = Number(startBarCode.slice(0, -1))
  if (endBarCode) {
    const endNum = Number(endBarCode.slice(0, -1))
    return endNum - startNum + 1
  } else {
    return 1
  }
}

const { scrollTop, flag, getScroll, getToTop } = useScrollViewBackTop()

const handleModalOk = () => {
  if (isLoading.value) return
  if (model.email === '') {
    uni.showToast({
      icon: 'none',
      title: '请输入邮箱地址',
    })
  } else if (!validateMail(model.email)) {
    uni.showToast({
      icon: 'none',
      title: '请输入正确的邮箱地址',
    })
  } else {
    isLoading.value = true
    uni.showLoading({
      title: '提交申请中',
    })
    downloadByOrderApi({
      downloadType: 2,
      email: model.email,
      orderId,
      userId: userId.value,
    })
      .then(() => {
        downloadEmail.value = model.email
        // showSuccess('已安排发送到邮箱')
        uni.hideLoading()
        isModalShow.value = false
        useMsgModalStore.alert({
          title: '已安排发送到邮箱',
          content:
            '鉴于条码制作需要时间，且邮件收发速度受网络影响，如果长时间没有收到邮件，请再提交申请，或咨询客服。谢谢！',
        })
      })
      .catch(() => {
        uni.hideLoading()
      })
      .finally(() => {
        isLoading.value = false
      })
  }
}
</script>
<template>
  <up-modal
    :show="isModalShow"
    title="选择下载方式"
    confirmText="提交邮箱"
    showCancelButton
    closeOnClickOverlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <view>
      <view class="text-left">
        <view class="font-bold">方式一：通过邮件下载</view>
        <up-input v-model="model.email" border="bottom" placeholder="请输入接收邮箱"></up-input>
        <view class="font-bold mt-6">方式二：通过电脑下载</view>
        <view class="flex text-xs">
          用电脑打开下面网站，前往【条码制作】-【条码下载】，直接下载。
        </view>
        <view
          class="flex gap-1 justify-center items-center py-2"
          @click="handleCopy('www.gs1helper.com')"
        >
          <view class="px-6 py-1 o-barcode-gray-card rounded">www.gs1helper.com</view>
          <up-tag :size="mini" plain type="warning" text="点击复制"></up-tag>
        </view>
      </view>
    </view>
  </up-modal>
  <scroll-view
    :scroll-y="true"
    class="f-scroll-box o-bg-no relative"
    :scroll-top="scrollTop"
    @scroll="getScroll"
    :refresher-enabled="true"
    :refresher-triggered="refreshTriggered"
    @refresherrefresh="onRefresh"
    :scroll-with-animation="true"
  >
    <view class="px-3 pt-3" v-for="item in data" :key="item.orderCode">
      <view class="bg-white p-4 rounded">
        <view class="flex items-center">
          <view class="flex-grow-1">
            <view class="o-color-aid text-xs">成交时间：{{ item.payDate }}</view>
            <view class="o-color-aid text-xs">订单编号：{{ item.orderCode }}</view>
          </view>
          <view
            class="px-6 py-2 text-sm flex items-center o-bg-primary justify-center color-white rounded"
            @click="handleDown(item.orderId)"
          >
            下载
          </view>
        </view>
        <view class="o-line mt-2 mb-2"></view>
        <view class="flex items-center mb-2 o-row-scroll">
          <view class="o-barcode-gray-card rounded">{{ item.startBarCode }}</view>
          <template v-if="item.endBarCode !== item.startBarCode">
            <view>~</view>
            <view class="o-barcode-gray-card rounded">{{ item.endBarCode }}</view>
          </template>
        </view>
        <view class="flex justify-between items-end">
          <view class="text-xs">放大系数为：{{ item.size }}</view>

          <view class="flex justify-end">
            <view class="flex items-baseline">
              <view class="text-sm">合计：</view>
              <view class="font-bold">
                {{ getQuantity(item.startBarCode, item.endBarCode) }}
              </view>
              <view class="text-sm ml-1">张</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="p-3"></view>
    <up-empty
      v-if="data?.length === 0"
      icon="https://wx.gs1helper.com/images/common/content.png"
      text="暂无订单"
    ></up-empty>
    <view v-if="showEmptyIcon" class="w-full o-color-aid text-center text-xs">- 已经到底了 -</view>
  </scroll-view>
  <back-top v-if="flag" @tap="getToTop" />
</template>

<style lang="scss" scoped>
.f-scroll-box {
  height: calc(100vh - 2.9rem);
}
</style>
