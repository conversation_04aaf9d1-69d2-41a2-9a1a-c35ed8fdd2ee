<script setup lang="ts">
import { onHide, onLaunch, onShow } from '@dcloudio/uni-app'

const checkUpdate = () => {
  // #ifdef MP-WEIXIN
  // 在微信环境下执行
  if (typeof wx !== 'undefined' && wx.canIUse('getUpdateManager')) {
    const updateManager = wx.getUpdateManager()

    // 监听检查更新事件
    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        console.log('检测到新版本，开始下载')
      }
    })

    // 新版本下载完成
    updateManager.onUpdateReady(() => {
      updateManager.applyUpdate()
    })

    // 新版本下载失败
    updateManager.onUpdateFailed(() => {
      uni.showModal({
        title: '更新失败',
        content: '新版本下载失败，请检查网络后重试',
        showCancel: false,
      })
    })
  } else {
    // 兼容低版本提示
    uni.showModal({
      title: '提示',
      content: '当前微信版本过低，请升级后使用',
      showCancel: false,
    })
  }
  // #endif
}

onLaunch(() => {
  console.log('App Launch')
})
onShow(() => {
  checkUpdate()
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
@import 'uview-plus/index.scss';
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
