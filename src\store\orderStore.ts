import { defineStore } from 'pinia'
import { ref } from 'vue'
import { CouponType, ServerType } from '@/enums'
import { CouponPageRes } from '@/service/orderApi'

export const orderStore = defineStore('orderStore', () => {
  const couponId = ref(0)
  const couponType = ref<CouponType>(0) // 类型：1：代金券，2：折扣劵，0用于默认为优惠券
  const discount = ref(0)
  const couponPrice = ref(0)

  // 厂商识别代码，用于支付时筛选优惠券
  const vendorCode = ref('')

  // 是否选择了优惠券，用于实际手机运行支付后，先到 orderConfirm 页面，会出发到 onShow 导致重复调用接口
  const isSelectedCoupon = ref(false)

  // orderInfo
  const orderCode = ref('')
  const serverType = ref<ServerType>() // 服务类型，1：条码制作，2：信息上报

  // TODO 将条码制作的参数拆分开一个store
  const startBarCode = ref('')
  const endBarCode = ref('')
  const size = ref('')
  const price = ref(0)
  const priceUnit = ref('元')
  const useCount = ref(0)
  const total = ref(0)
  const existCount = ref(0)
  const totalPrice = ref(0)
  const actuallyPrice = ref(0)
  const discountsPrice = ref(0)

  const tempName = ref('')
  const tempType = ref(0) // 模版类型：1：条码制作,2:信息上报,3:设计

  const clearOrderInfo = () => {
    // orderCode.value = ''
    startBarCode.value = ''
    endBarCode.value = ''
    size.value = ''
    price.value = 0
    priceUnit.value = '元'
    useCount.value = 0
    total.value = 0
    existCount.value = 0
    totalPrice.value = 0
    actuallyPrice.value = 0
    discountsPrice.value = 0
    tempName.value = ''
    tempType.value = 0
  }

  const clearCouponInfo = () => {
    couponId.value = 0
    couponType.value = 0
    discount.value = 0
    couponPrice.value = 0
  }

  return {
    couponId,
    serverType,
    orderCode,
    vendorCode,
    startBarCode,
    endBarCode,
    size,
    price,
    priceUnit,
    useCount,
    total,
    existCount,
    totalPrice,
    actuallyPrice,
    discountsPrice,
    couponType,
    discount,
    couponPrice,
    isSelectedCoupon,
    tempName,
    tempType,
    clearCouponInfo,
    clearOrderInfo,
  }
})
