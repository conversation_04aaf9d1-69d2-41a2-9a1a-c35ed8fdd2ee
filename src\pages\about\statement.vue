<route lang="json5">
{
  style: {
    navigationBarTitleText: '平台免责声明',
  },
}
</route>
<script lang="ts" setup>
import { Article } from '@/enums'
import { articleLoadApi } from '@/service/tutorialsApi'
import { addLineHeight, updateVideoDimensions } from '@/utils/tool'

const { windowWidth: windowWidthPx } = uni.getSystemInfoSync()
const paddingRpx = 48
const paddingTwoPx = (paddingRpx * 2 * windowWidthPx) / 750
const content = ref<string[]>([])
const title = ref<string[]>([])

// 返回的顺序不一定按这个顺序
const ArticleIds = [
  Article.platform,
  Article.privacy,
  Article.film,
  Article.infoReport,
  Article.design,
]

ArticleIds.forEach((id) => {
  articleLoadApi({
    articleId: id,
  }).then((res) => {
    // 给视频适配宽度和高度
    const r = updateVideoDimensions(res.data.articleConent)
    // 添加默认行高
    content.value.push(addLineHeight(r))
    title.value.push(res.data.articleTitle)
  })
})
</script>
<template>
  <view class="f-page bg-white p-6">
    <view v-for="(item, index) in content" :key="index">
      <view class="text-base font-bold mt-6 mb-4 text-center">
        {{ title[index] }}
      </view>
      <up-parse :content="item"></up-parse>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-page {
  min-height: 100vh;
}
</style>
