<script setup lang="ts">
defineProps<{
  carousesList: string[]
}>()

const current = ref(0)
const { windowWidth } = uni.getSystemInfoSync()
const handleSelectImg = (index: number) => {
  current.value = index
}
</script>

<template>
  <view class="bg-white">
    <up-swiper
      v-model:current="current"
      :height="windowWidth"
      :list="carousesList"
      :autoplay="false"
    ></up-swiper>
    <view class="px-4 py-3 flex gap-2">
      <image
        class="f-cs-img"
        v-for="(item, index) in carousesList"
        :style="{ opacity: current === index ? 1 : 0.4 }"
        :src="item"
        :key="index"
        mode="aspectFill"
        @click="handleSelectImg(index)"
      ></image>
    </view>
  </view>
</template>

<style scoped lang="scss">
.f-cs-img {
  $w: 100rpx;
  width: $w;
  height: $w;
}
</style>
