import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface RefundGoodsInfo {
  orderCode: string
  goodsName?: string
  orderContent?: string
  goodsImage?: string
  payDate?: string
}

export const useRefundStore = defineStore('refundStore', () => {
  const refundGoodsInfo = ref<RefundGoodsInfo>({
    orderCode: '',
    goodsName: '',
    orderContent: '',
    goodsImage: '',
    payDate: '',
  })

  // 设置售后申请商品信息
  const setRefundGoodsInfo = (info: RefundGoodsInfo) => {
    refundGoodsInfo.value = info
  }

  // 清空售后申请商品信息
  const clearRefundGoodsInfo = () => {
    refundGoodsInfo.value = {
      orderCode: '',
      goodsName: '',
      orderContent: '',
      goodsImage: '',
      payDate: '',
    }
  }

  return {
    refundGoodsInfo,
    setRefundGoodsInfo,
    clearRefundGoodsInfo,
  }
})
