import { defineStore } from 'pinia'
import { OrderOtherInfoListResData } from '@/service/agencyServiceApi'

export const submitServiceStore = defineStore('submitServiceStore', () => {
  const orderInfoData = ref<OrderOtherInfoListResData>({
    barCodeCardNum: '',
    barCodeCardPassword: '',
    companyLicenseChangeImage: '',
    companyLicenseChangeUrl: '',
    companyLicenseImage: '',
    companyLicenseUrl: '',
    contact: '',
    contactPhone: '',
    email: '',
    isHasChange: true,
    orderCode: '',
    orderContent: '',
    orderId: 0,
    otherStatus: 0,
    otherStatusStr: '',
    payCetificate: '',
    payCetificateImage: '',
    payDate: '',
    reason: '',
    registrationForm: '',
    registrationFormImage: '',
    legalPhone: '',
  })

  return { orderInfoData }
})
