<script setup lang="ts">
import { useDeviceOrientation } from '@vueuse/core'

const props = defineProps<{
  scale: number
}>()

const imgWidth = 675
const imgHeight = 360
const k = 5 // 缩小系数

// const x = ref(-48)
// const y = ref(-imgHeight/k + 8.1)
const width = ref(imgWidth / k)
const height = ref(imgHeight / k)

// x 轴 beta：沿着屏幕表面，向右为正，向左为负。表示设备围绕 x 轴的旋转角度，范围为 -180 到 180 度。
// 此参数描述设备前后翻转的情况。当 beta 为 0° 时，设备保持水平，当设备向前翻转时，该值会增加到 180°，
// 向后翻转时则减少到 -180°
// y 轴 gamma：沿着屏幕表面，向上为正，向下为负。表示设备围绕 y 轴的旋转角度，范围为 -90 到 90 度。
// 此参数描述设备左右倾斜的情况。当 gamma 为 0° 时，设备左右两边与地表面的距离相等；向右倾斜时，该值增
// 加到 90°，向左倾斜时则减少到 -90°
// z 轴 alpha：垂直于屏幕表面，远离屏幕的方向为正，朝向屏幕的方向为负。表示设备围绕 z 轴的旋转角度，
// 范围为 0 到 360 度。此参数指示设备顶部指向的方向，相对于地理北方的角度。当 alpha 为 0° 时，设备顶
// 部指向正北方向。
const { isSupported, alpha, beta, gamma } = useDeviceOrientation()

const x = computed(() => {
  // gamma 取-60~60
  const v1 = -60
  const v2 = 60
  const original = -48 // 对应v1原始值
  let r = original
  if (isSupported.value) {
    // y = kx + b
    const k = (v2 - v1) / 37.6
    const b = k * v1 + original
    const g = gamma.value
    /*    if(g < v1 ){
      g = v1
    }
    else if(g > v2 ){
      g = v2
    } */
    r = k * g + b
  }
  return r
})

const y = computed(() => {
  // beta 为 0° 时，设备保持水平，当设备向前翻转时，该值会增加到 180°，向后翻转时则减少到 -180°。
  // 取0-90为限, 0是平放，90是竖起来放垂直地面
  const v1 = 0
  const v2 = 90
  const original = -height.value + 8.1 // 对应v1原始值
  let r = original
  if (isSupported.value) {
    // y = kx + b
    const k = (height.value - 8.1) / (v2 - v1)
    const b = k * v1 + original
    let g = beta.value
    if (g < v1) {
      g = v1
    } else if (g > v2) {
      g = v2
    }
    r = k * g + b
  }
  return r
})

const style = computed(() => {
  return {
    backgroundSize: width.value + 'vw' + ' ' + height.value + 'vw',
    backgroundPositionX: x.value + 'vw',
    backgroundPositionY: y.value + 'vw',
    '--s': props.scale,
  }
})
</script>

<template>
  <!-- <view style="margin-top: 3vw" class="f-tag-card text-white font-bold px-2 py-0.5 rounded">
        智联
      </view> -->
  <div class="f-code-top w-full o-flex-c" :style="style">
    <img
      src="/resource/images/shop/oneCodeTitle.svg"
      class="f-mix-font"
      alt="一物一码 · 数字认证"
    />
  </div>
</template>

<style scoped lang="scss">
.f-code-top {
  height: calc(8.1vw * var(--s, 0.5));
  background-color: #000;
  background-image: url('/resource/images/rainbow.jpg');
  background-repeat: repeat;
  transition: all 0.1s linear;
}

.f-mix-font {
  width: calc(27.4vw * var(--s, 0.5));
  height: calc(4vw * var(--s, 0.5));
  mix-blend-mode: difference;
  transition: all 0.1s linear;
}
</style>
