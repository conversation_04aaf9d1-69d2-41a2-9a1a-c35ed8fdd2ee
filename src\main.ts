import '@/style/index.scss'
import 'uno.css'
import { createSSRApp } from 'vue'
import App from './App.vue'
import { prototypeInterceptor, requestInterceptor } from './interceptors'
import store from './store'
import '@/style/myStyle.scss'
import uviewPlus from 'uview-plus'
import { PublicImgPath } from './components/image'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  // app.use(routeInterceptor) // 不需要路由守卫
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(uviewPlus)
  /* app.use(uviewPlus, () => {
    return {
      options: {
        // 修改config对象的属性
        config: {
          // 默认字体图标自托管资源地址
          // iconUrl: PublicImgPath + 'font_2225171_8kdcwk4po24.ttf',
          iconUrl: '/static/font_2225171_8kdcwk4po24.ttf',
        },
      },
    }
  }) */
  return {
    app,
  }
}
