<route lang="json5">
{
  style: {
    navigationBarTitleText: '支付成功',
    backgroundColor: '#f0f3f8',
  },
}
</route>

<script lang="ts" setup>
import { Color } from '@/enums/colorEnum'

const orderCode = ref('')

onLoad((option) => {
  if (option?.orderCode) {
    orderCode.value = option.orderCode
  }
})

// 返回首页或指定页面
const goBack = () => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

// 查看订单
const goToOrder = () => {
  uni.switchTab({
    // 这样才有后退打底
    url: '/pages/userPage/index',
    success: () => {
      uni.navigateTo({
        url: '/pages/myOrder/index',
      })
    },
  })
}
</script>

<template>
  <view class="flex flex-col items-center px-5 py-10 bg-white min-h-screen">
    <!-- 成功图标 -->
    <view class="mt-15 mb-6">
      <up-icon name="checkmark-circle" :color="Color.primary" :size="80"></up-icon>
    </view>

    <!-- 成功信息 -->
    <view class="w-full text-center mb-10">
      <view class="text-2xl font-bold text-primary mb-3.75">支付成功</view>
      <view class="text-sm text-gray" v-if="orderCode">订单编号：{{ orderCode }}</view>
    </view>

    <!-- 分割线 -->
    <view class="w-full h-px bg-gray-100 my-5"></view>

    <!-- 操作按钮 -->
    <view class="flex w-full justify-center mt-6 space-x-4">
      <view
        class="px-5 py-2 rounded-sm flex items-center justify-center bg-gray-100"
        @click="goBack"
      >
        返回首页
      </view>
      <view
        class="px-5 py-2 rounded-sm flex items-center justify-center bg-primary text-white"
        @click="goToOrder"
      >
        查看订单
      </view>
    </view>
  </view>
</template>
