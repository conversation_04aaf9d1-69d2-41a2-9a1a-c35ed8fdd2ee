<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的信息',
  },
}
</route>
<script lang="ts" setup></script>
<template>
  <view class="p-4">
    <up-cell-group custom-class="bg-white py-2 rounded" :border="false">
      <up-cell title="开票信息" is-link url="/pages/invoiceTemple/index" />
      <!--  #ifndef MP-TOUTIAO-->
      <up-cell title="收货地址" is-link url="/pages/addressPage/index" :border="false" />
      <!--  #endif-->
    </up-cell-group>
  </view>
</template>

<style lang="scss" scoped></style>
