import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface CommentInfo {
  orderId: number
  orderCode: string
  goodsName: string
  goodsImage: string
  orderContent: string
}

export const useCommentStore = defineStore('comment', () => {
  // 评价信息
  const commentInfo = ref<CommentInfo>({
    orderId: 0,
    orderCode: '',
    goodsName: '',
    goodsImage: '',
    orderContent: '',
  })

  // 设置评价信息
  function setCommentInfo(info: CommentInfo) {
    commentInfo.value = info
  }

  // 清空评价信息
  function clearCommentInfo() {
    commentInfo.value = {
      orderId: 0,
      orderCode: '',
      goodsName: '',
      goodsImage: '',
      orderContent: '',
    }
  }

  return {
    commentInfo,
    setCommentInfo,
    clearCommentInfo,
  }
})
