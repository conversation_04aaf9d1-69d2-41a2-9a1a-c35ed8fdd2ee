<route lang="json5">
{
  style: {
    navigationBarTitleText: '条码使用说明',
  },
}
</route>
<script lang="ts" setup>
const { windowWidth: windowWidthPx } = uni.getSystemInfoSync()
const paddingRpx = 48
const paddingTwoPx = (paddingRpx * 2 * windowWidthPx) / 750

const getWidth = () => {
  return windowWidthPx - paddingTwoPx
}
const getHeight = () => {
  const imgSourceWidth = 929
  const imgSourceHeight = 650
  const swiperWidthPx = windowWidthPx - paddingTwoPx
  return (swiperWidthPx * imgSourceHeight) / imgSourceWidth
}

const previewImg = () => {
  uni.previewImage({
    urls: ['https://wx.gs1helper.com/images/p_filmSpecification_table.png'],
    current: 0,
  })
}
</script>
<template>
  <view class="bg-white overflow-hidden px-6 pt-8 pb-10 text-xs">
    <view class="text-lg font-bold text-center mb-6">条码使用说明</view>
    <view class="o-p mb-6">
      商品条码原条码压缩包中，每个商品条码包含一个一维码数字条码 EPS 和一个用于浏览的 SVG 版本。EPS
      格式的条码文件是数字条码矢量图，需要用与其适配的专业软件（Adobe Illustrator 或者
      CorelDRAW）查看，建议
      <text class="font-bold o-color-danger">不要</text>
      用 Photoshop 软件打开或者编辑数字条码，可能会造成文件损伤。
    </view>
    <view class="font-bold mb-3">注意事项：</view>
    <view class="flex gap-1 mb-6">
      <view class="o-color-primary font-bold flex-shrink-0">1.</view>
      <view>
        EAN-13
        数字条码虚线框表示条码符号尺寸示意图。虚线框使用时可删除，删除后应保留框内范围,并确保左右空白区预留足够。即左右空白区需大于或等于虚线标记范围。
      </view>
    </view>
    <view class="f-img-1"></view>
    <view class="text-center o-color-aid mb-6">图 1 EAN-13 条码符号结构</view>
    <view class="flex gap-1 mb-3">
      <view class="o-color-primary font-bold flex-shrink-0">2.</view>
      <view>供人识别字符默认使用字体 Arial Narrow，如需更改字体，应确保条码符号显示正确可读。</view>
    </view>
    <view class="flex gap-1 mb-3">
      <view class="o-color-primary font-bold flex-shrink-0">3.</view>
      <view>如需更改条码符号颜色，请参照《GB 12904-2008 商品条码 零售商品编码与条码表示》。</view>
    </view>
    <view class="flex gap-1 mb-3">
      <view class="o-color-primary font-bold flex-shrink-0">4.</view>
      <view>商品条码符号放置，请参照《GB/T 14257-2009 商品条码 条码符号放置指南》。</view>
    </view>
    <view class="flex gap-1 mb-6">
      <view class="o-color-primary font-bold flex-shrink-0">5.</view>
      <view>
        <text class="font-bold o-color-danger">
          禁止随意更改条码尺寸。若因随意更改条码尺寸，出现问题概不负责。
        </text>
        请参考《表 1》选择合适的放大系数进行条码制作。
      </view>
    </view>
    <view class="font-bold mb-3">附: 条码放大系数与EAN商品条码符号主要尺寸对照表：</view>
    <up-image
      :height="getHeight()"
      :width="getWidth()"
      src="https://wx.gs1helper.com/images/p_filmSpecification_table.png"
      @click="previewImg"
    ></up-image>
    <view class="text-center o-color-aid mb-6">表 1 尺寸对照表</view>
  </view>
</template>

<style lang="scss" scoped>
.f-img-1 {
  width: 100%;
  height: 40vw;
  background-image: url('https://wx.gs1helper.com/images/p_filmSpecification_1.jpg');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}
</style>
