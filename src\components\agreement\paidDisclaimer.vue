<script lang="ts" setup>
import { articleLoadApi } from '@/service/tutorialsApi'
import { addLineHeight, updateVideoDimensions } from '@/utils/tool'

const props = defineProps({
  articleIds: Array<number>,
})

const content = ref<string[]>([])
const title = ref<string[]>([])

onMounted(() => {
  props.articleIds.forEach((id) => {
    articleLoadApi({
      articleId: id,
    }).then((res) => {
      // 给视频适配宽度和高度
      const r = updateVideoDimensions(res.data.articleConent)
      // 添加默认行高
      content.value.push(addLineHeight(r))
      title.value.push(res.data.articleTitle)
    })
  })
})
</script>
<template>
  <scroll-view class="f-h overflow-scroll" :scroll-y="true">
    <view class="mb-6" v-for="(item, index) in content" :key="index">
      <view
        class="text-center text-base font-bold mb-4"
        style="word-break: break-all; line-break: anywhere"
      >
        {{ title[index] }}
      </view>
      <up-parse class="text-xs text-left" :content="item"></up-parse>
    </view>
  </scroll-view>
</template>

<style lang="scss" scoped>
.f-h {
  max-height: 50vh;
}
</style>
