import { defineStore } from 'pinia'
import { ref } from 'vue'
import { BaseProjectType } from '@/components/descriptionStr'

export const useServiceStore = defineStore('serviceStore', () => {
  // 转跳makeFilm判断是做什么类型服务，跟ServerType的区别是makeFilm分成了EAN13、ITF14
  const descriptionServiceType = ref<BaseProjectType['descriptionServiceType']>()
  // 胶片制作是否附加通报业务
  const isHasOtherServer = ref(true)
  // 是否首张胶片
  const isFirstCode = ref(false)

  const barCodePrice = ref(0) // 条码总金额
  const extraPrice = ref(0) // 额外金额，例如加上通报组成套餐

  return {
    descriptionServiceType,
    isHasOtherServer,
    barCodePrice,
    extraPrice,
    isFirstCode,
  }
})
