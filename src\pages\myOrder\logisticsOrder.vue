<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '物流订单',
    enablePullDownRefresh: true,
    backgroundColor: '#f0f3f8',
  },
}
</route>
<script lang="ts" setup>
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { storeToRefs } from 'pinia'
import { AllOrderPageRes } from '@/service/orderApi'
import { OrderDirection } from '@/enums/httpEnum'
import { useUserStore } from '@/store/user'
import { getServerTypeStr, PayState, ServerType } from '@/enums'
import { makeFilmAndReportServiceDescription } from '@/components/descriptionStr'
import { expressOrderPageApi, ExpressOrderPageRes } from '@/service/shoppingMallApi'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList, orderSuccess } = storeToRefs(useInvoiceOrderStore)
const showEmptyIcon = ref(false)
const orderCode = ref([])
const page = ref(1)
const list = ref<ExpressOrderPageRes['data']>([])
const filterCurrent = ref('全部')

const init = () =>
  new Promise((resolve, reject) => {
    list.value = []
    orderCode.value = []
    orderObjList.value = []
    run()
      .then(() => {
        list.value = [...data.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })

onLoad(() => {
  // 页面加载,清空orderCode，后退到此页面时，不触发onLoad
  init()
})

onShow(() => {
  // 订单成功后退触发
  if (orderSuccess.value) {
    orderSuccess.value = false
    page.value = 1
    init()
  }
})

// 下拉刷新
onPullDownRefresh(() => {
  showEmptyIcon.value = false
  page.value = 1
  init().finally(() => {
    uni.stopPullDownRefresh()
  })
})

const getIsInvoiced = () => {
  switch (filterCurrent.value) {
    case '全部':
      return null
    case '未开票':
      return 0
    case '已开票':
      return 1
    default:
      return null
  }
}

const { loading, error, data, run } = useRequest<ExpressOrderPageRes>(
  () =>
    expressOrderPageApi({
      needTotalCount: true,
      orderBy: 'payDate',
      orderDirection: OrderDirection.desc,
      pageIndex: page.value,
      pageSize: 50,
      // payState: PayState.paid,
      userId: userId.value,
    }),
  /*   allOrderPageApi({
    // serverType:
    isInvoiced: getIsInvoiced(),
    userId: userId.value,
    payState: PayState.paid,
    groupBy: '',
    needTotalCount: true,
    // orderBy: 'payDate',
    orderDirection: OrderDirection.desc,
    pageIndex: page.value,
    pageSize: 50,
  }), */
)

// 滚到页面底部加载更多
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    run().then(() => {
      list.value = [...list.value, ...data.value]
      if (data.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

const handleToLogistics = (orderCode: string) => {
  uni.navigateTo({
    url: '/pages/myOrder/logisticsDetail?orderCode=' + orderCode,
  })
}
</script>
<template>
  <view class="px-4 pt-3 pb-10">
    <view
      class="bg-white p-4 rounded mt-3"
      v-for="item in list"
      :key="`${item.serverType}${item.orderId}`"
    >
      <view class="o-color-aid text-xs">成交时间：{{ item.payDate }}</view>
      <view class="o-color-aid text-xs">订单编号：{{ item.orderCode }}</view>
      <view class="o-line mt-2 mb-2"></view>
      <view class="font-bold mb-1">{{ getServerTypeStr(item.serverType) }}：</view>
      <view>{{ item.orderContent }}</view>
      <view class="flex justify-between items-end gap-3">
        <view></view>
        <view class="flex shrink-0 justify-end">
          <view
            class="px-6 py-2 text-sm center o-bg-no rounded"
            @click="handleToLogistics(item.orderCode)"
          >
            查看物流
          </view>
        </view>
      </view>
    </view>
    <up-empty
      v-if="list?.length === 0"
      icon="https://wx.gs1helper.com/images/common/search.png"
      text="暂无订单"
    ></up-empty>
  </view>
  <view class="w-full o-color-aid text-center" v-if="showEmptyIcon">- 已经到底了 -</view>
  <view class="p-10"></view>
</template>

<style lang="scss" scoped></style>
